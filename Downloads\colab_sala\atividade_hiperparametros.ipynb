import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn import svm
from sklearn.naive_bayes import GaussianNB
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import ConfusionMatrixDisplay, accuracy_score, precision_score, recall_score
from sklearn.metrics import precision_recall_curve
from sklearn.model_selection import GridSearchCV, RandomizedSearchCV
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns

# Carrega o dataset Titanic
df_titanic = pd.read_csv("titanic.csv")

# Seleciona colunas relevantes
df = df_titanic[['Pclass', 'Sex', 'Age', 'Fare', 'Survived']]

# Preenche os valores nulos na coluna 'Age' com a média
df['Age'].fillna(df['Age'].mean(), inplace=True)

# Converter a coluna 'Sex' para valores numéricos (0 = feminino, 1 = masculino)
df['Sex'] = df['Sex'].apply(lambda x: 1 if x == 'male' else 0)

# Separar os dados em features (X) e target (y)
X = df[['Pclass', 'Sex', 'Age', 'Fare']]
y = df['Survived']

# Dividir o conjunto de dados em treino e teste
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

sns.pairplot(df, hue='Survived')

# Remova os comentários e rode o Grid Search para encontrar os melhores hiperparâmetros

#param_grid = {} # adicione os hiperparâmetros que deseja testar, junto com a lista de valores
# Dica: você pode usar também o np.linspace().
# Lembre-se que alguns parâmetros do RandomForest precisam ser inteiros. Então, converta os valores para int antes de usar

#grid = GridSearchCV(RandomForestClassifier(), param_grid, verbose=3)
#grid.fit(X_train, y_train)
#print(grid.best_params_)
#print(grid.best_estimator_)  # retorna um objeto RandomForestClassifier() já otimizado

# Cria o modelo de Random Forest: aplique os melhores hiperparâmetros encontrados
model_rf = RandomForestClassifier(n_estimators=3, max_features=1, min_samples_leaf=1, criterion='gini', random_state=42)


# Treina o modelo
model_rf.fit(X_train, y_train)

# Faz previsões
y_pred = model_rf.predict(X_test)

# Avalia o modelo
rf_accuracy = accuracy_score(y_test, y_pred)
rf_precision = precision_score(y_test, y_pred)
rf_recall = recall_score(y_test, y_pred)
print(f'Acuária do Random Forest: {rf_accuracy:.2f}')
print(f'Precisão do Random Forest: {rf_precision:.2f}')
print(f'Revocação (Recall) do Random Forest: {rf_recall:.2f}')

# Cria a matriz de confusão
ConfusionMatrixDisplay.from_estimator(model_rf, X_test, y_test, cmap = 'Blues', values_format='.2f', normalize='true')
plt.show()

import shap

explainer = shap.Explainer(model_rf)

nomes_colunas = ['Pclass', 'Sex', 'Age', 'Fare']
X_test_df = pd.DataFrame(X_test, columns=nomes_colunas)

shap_values = explainer(X_test_df)
print(f'Formato do array: {shap_values.shape}')

indice_amostra = 0   # escolha manualmente uma amostra entre 0 e 178 e veja quanto cada feature contribuiu para a classificação
print(f'Classe da amostra: {model_rf.predict([X_test[indice_amostra]])[0]}')
shap.plots.waterfall(shap_values[indice_amostra][:,0])  # influência da classe 0 (não-sobreviveu)
shap.plots.waterfall(shap_values[indice_amostra][:,1])  # influência da classe 1 (sobreviveu)


# O SVM é sensível à escala dos dados, portanto vamos transformá-los.
scaler = StandardScaler()
X_train = scaler.fit_transform(X_train)
X_test = scaler.transform(X_test)

# Remova os comentários e rode o Grid Search para encontrar os melhores hiperparâmetros

#param_grid = {} # adicione os hiperparâmetros que deseja testar, junto com a lista de valores
# Dica: você pode usar também o np.logspace()

#grid = GridSearchCV(svm.SVC(), param_grid, cv=5, n_jobs=-1, verbose=3)  # cv=5 é o valor padrão, ou seja, a validação é feita em 5-folds. Já n_jobs=-1 usa todas as threads do processador.
#grid.fit(X_train, y_train)
#print(grid.best_params_)
#print(grid.best_estimator_)   # retorna um objeto SVC() já otimizado

# Cria o modelo de SVM: aplique os melhores hiperparâmetros encontrados
model_svm = svm.SVC(kernel='rbf', C=1, gamma=0.1, random_state=42)

# Treina o modelo
model_svm.fit(X_train, y_train)

# Faz previsões
y_pred = model_svm.predict(X_test)

# Avalia o modelo
svm_accuracy = accuracy_score(y_test, y_pred)
svm_precision = precision_score(y_test, y_pred)
svm_recall = recall_score(y_test, y_pred)
print(f'Acuária do SVM: {svm_accuracy:.2f}')
print(f'Precisão do SVM: {svm_precision:.2f}')
print(f'Revocação (Recall) do SVM: {svm_recall:.2f}')

# Cria a matriz de confusão
ConfusionMatrixDisplay.from_estimator(model_svm, X_test, y_test, cmap = 'Blues', values_format='.2f', normalize='true')
plt.show()

# Otimização dos hiperparâmetros usando Grid Search ou Random Search

# Cria o modelo de SVM: aplique os melhores hiperparâmetros encontrados
modelo = svm.SVC(kernel='rbf', C=1, gamma=0.1, random_state=42)

# Treina o modelo
modelo.fit(X_train, y_train)

# Faz previsões
y_pred = modelo.predict(X_test)

# Avalia o modelo
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
print(f'Acuária do SVM: {accuracy:.2f}')
print(f'Precisão do SVM: {precision:.2f}')
print(f'Revocação (Recall) do SVM: {recall:.2f}')

# Cria a matriz de confusão
ConfusionMatrixDisplay.from_estimator(modelo, X_test, y_test, cmap = 'Blues', values_format='.2f', normalize='true')
plt.show()